import request from "@/utils/request";
import {doCrypt} from "@/utils/sm2Encrypt";

// 查询用户列表
export function listUser(params) {
    return request({
        url: "/user/staffOrgs",
        method: "get",
        params: params
    });
}

// 查询用户详细
export function getUser(staffId) {
    return request({
        url: "/user/staffs/" + staffId,
        method: "get"
    });
}

// 根据人员id和岗位id查询用户详细
export function getUserInfo(staffId,staffOrgId) {
    return request({
        url: "/user/staffs/getInfo/" + staffId + "/" + staffOrgId,
        method: "get"
    });
}

// 新增用户
export function addPortaluser(data) {
    return request({
        url: "/user/staffs",
        method: "post",
        data: data
    });
}

export function addUserForGroup(data) {
    return request({
        url: "/user/staffOrgs",
        method: "post",
        data: data
    });
}

export function delUserForGroup(data) {
    return request({
        url: "/user/staffOrgs/delete",
        method: "post",
        data: data
    });
}

export function addStaffOrgAndRole(data) {
    return request({
        url: "/user/staffOrgs/addStaffOrgAndRole",
        method: "post",
        data: data
    });
}

// 修改用户
export function updateUser(data) {
    return request({
        url: "/user/staffs/edit",
        method: "post",
        data: data
    });
}

// 删除用户
export function delUser(data) {
    return request({
        url: "/user/staffs/delete",
        method: "post",
        data: data
    });
}

// 查询用户所拥有的角色
export function findUserRoles(staffOrgId) {
    return request({
        url: "/user/staffRole/staffOrgs/staffRoles/" + staffOrgId,
        method: "get"
    });
}

// 用户密码重置
export async function resetUserPwd(staffId, newPassWord) {
    const data = {
        staffId: staffId,
        newPassWord: await doCrypt(newPassWord)
    };
    return request({
        url: "/user/staffs/reset",
        method: "post",
        data: data
    });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
    const data = {
        userId,
        status
    };
    return request({
        url: "/system/user/changeStatus",
        method: "put",
        data: data
    });
}

// 查询用户个人信息
export function getUserProfile() {
    return request({
        url: "/system/user/profile",
        method: "get"
    });
}

// 修改用户个人信息
export function updateUserProfile(data) {
    return request({
        url: "/user/staffs/edit",
        method: "post",
        data: data
    });
}

// 用户密码重置
export function updateUserPwd(data) {
    return request({
        url: "/user/staffs/change",
        method: "post",
        data: data
    });
}


// 用户头像上传
export function uploadAvatar(data) {
    return request({
        url: "/system/user/profile/avatar",
        method: "post",
        data: data
    });
}

// 下载用户导入模板
export function importTemplate() {
    return request({
        url: "/system/user/importTemplate",
        method: "get"
    });
}

export function addUserRoles(staffOrgId, roleId) {
    return request({
        url: "/user/staffRole/staffOrgs/" + staffOrgId + "/" + roleId,
        method: "post"
    });
}

export function delUserRoleInfo(staffOrgId, roleId) {
    return request({
        url: "/user/staffRole/delStaffRole/" + staffOrgId + "/" + roleId,
        method: "post"
    });
}


export function getInfo(params) {
    return request({
        url: "/auth/oauth/check_token",
        method: "get",
        params
    });
}

export function enableOrDisablePortaluser(data) {
    return request({
        url: "/user/staffs/edit",
        method: "post",
        data: data
    });
}

// 获取短信验证码
export function getVerificationCode(data) {
    return request({
        url: "/user/staffs/getVerificationCode",
        method: "post",
        data,
        headers: {
            isToken: false
        }
    });
}

// 修改密码
export function forgottenPassword(data) {
    return request({
        url: "/user/staffs/forgottenPassword",
        method: "post",
        data
    });
}

// 导入
export function upload(data) {
    return request({
        url: "/user/staffs/upload",
        method: "post",
        data
    });
}


// 查询人员列表
export function queryUserList(data) {
  return request({
    url: "/user/staffs/selectUserList", 
    method: "post",
    data: data,
  })
}

// 查询正式人员列表
export function listFormalSysUser(params) {
    return request({
        url: "/user/sysUser/formalStaffList",
        method: "post",
        data: params
    });
}


// 模糊查询正式人员
export function listFormalStaff(params) {
    return request({
        url: "/user/sysUser/selectFormalStaff",
        method: "post",
        data: params
    });
}


// 查询借调人员列表
export function listLoanSysUser(params) {
    return request({
        url: "/user/sysUser/loanStaffList",
        method: "post",
        data: params
    });
}


// 查询外协人员列表
export function listOutsourceSysUser(params) {
    return request({
        url: "/user/sysUser/outsourceStaffList",
        method: "post",
        data: params
    });
}


// 模糊查询外协人员
export function listOutsourceStaff(params) {
    return request({
        url: "/user/sysUser/selectOutsourceStaff",
        method: "post",
        data: params
    });
}


// 修改系统人员
export function updateSysUser(data) {
    return request({
        url: "/user/sysUser/edit",
        method: "post",
        data: data
    });
}


// 新增借调人员
export function addLoanStaff(data) {
    return request({
        url: "/user/sysUser/addLoanStaff",
        method: "post",
        data: data
    });
}


// 新增外协人员
export function addOutsourceStaff(data) {
    return request({
        url: "/user/sysUser/addOutsourceStaff",
        method: "post",
        data: data
    });
}



// 删除人员
export function delStaff(data) {
    return request({
        url: "/user/sysUser/delStaff",
        method: "post",
        data: data
    });
}
