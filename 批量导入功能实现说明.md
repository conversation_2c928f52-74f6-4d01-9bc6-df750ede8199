# 借调人员批量导入功能实现说明

## 功能概述

已成功为借调人员页面（`src/systemViews/system/user/loanIndex.vue`）添加批量导入功能，实现了从Excel文件批量导入借调人员数据的需求。

## 实现的功能

### 1. 批量导入按钮
- 在搜索按钮区域添加了"批量导入"按钮
- 使用了图标美化，与现有界面风格保持一致
- 添加了权限控制 `v-hasPermi="['sys:base:user:add']"`

### 2. 批量导入对话框
- 宽度：500px，居中显示
- 包含表单验证功能
- 响应式布局设计

### 3. 必填项配置
根据需求，修改了必填项为：

#### 人员类型（选择框）
- 使用 `el-select` 组件
- 数据源：`staff_type` 字典
- 支持清空选择
- 必填验证

#### 岗位（输入框）
- 使用 `el-input` 组件
- 文本输入框
- 必填验证

### 4. 模板下载功能
- 提供"批量导入借调人员模版"下载链接
- 使用 UploadFilled 图标
- 点击即可下载Excel模板

### 5. 文件上传功能
- 使用自定义 `UploadFile` 组件
- 支持 `.xlsx` 和 `.xls` 格式
- 文件大小和格式验证
- 上传进度显示
- 错误处理机制

## 技术实现

### 前端代码修改

#### 1. 模板部分
```vue
<!-- 批量导入按钮 -->
<el-button @click="handleBatchImport" v-hasPermi="['sys:base:user:add']">
  <i class="icon iconfont icon-piliangchongzhi icon-btn"></i>
  批量导入
</el-button>

<!-- 批量导入对话框 -->
<el-dialog :title="'批量导入借调人员'" v-model="batchImportOpen" width="500px" append-to-body>
  <!-- 表单内容 -->
</el-dialog>
```

#### 2. 脚本部分
```javascript
// 批量导入相关变量
const batchImportOpen = ref(false);
const batchImportFormRef = ref(null);
const uploadRef = ref(null);
const batchFileList = ref([]);

// 批量导入表单数据
const batchImportForm = ref({
  staffType: "",
  post: "",
  tenantId: userStore.userInfo.tenantId,
});

// 表单验证规则
const batchImportRules = {
  staffType: [{ required: true, message: "请选择人员类型", trigger: "change" }],
  post: [{ required: true, message: "请输入岗位", trigger: "blur" }],
};
```

#### 3. 核心方法
- `handleBatchImport()` - 打开批量导入对话框
- `importTemplate()` - 下载导入模板
- `handleFileSuccess()` - 文件上传成功处理
- `handleFileError()` - 文件上传失败处理
- `submitBatchImport()` - 提交批量导入
- `cancelBatchImport()` - 取消批量导入

### API接口

#### 新增接口
```javascript
// 批量导入借调人员
export function batchImportLoanStaff(data) {
    return request({
        url: "/user/sysUser/batchImportLoanStaff",
        method: "post",
        data: data
    });
}

// 下载借调人员导入模板
export function loanStaffImportTemplate() {
    return request({
        url: "/user/sysUser/loanStaffImportTemplate",
        method: "get"
    });
}
```

### 样式优化
```css
.icon-btn {
  font-size: 16px;
  margin-right: 5px;
}

.a-link {
  color: #c20000;
  align-items: center;
  cursor: pointer;
}

.flex {
  display: flex;
  align-items: center;
}
```

## 文件修改清单

### 1. 主要文件
- `src/systemViews/system/user/loanIndex.vue` - 主页面文件
- `src/api/system/user.js` - API接口文件

### 2. 新增导入
```javascript
import UploadFile from "@/components/UploadFile/index";
import { ElMessage } from "element-plus";
import { formatMinuteTime } from "@/utils";
import { UploadFilled } from "@element-plus/icons-vue";
```

## 使用流程

1. 用户点击"批量导入"按钮
2. 弹出批量导入对话框
3. 选择人员类型（必填）
4. 输入岗位信息（必填）
5. 点击模板下载链接获取Excel模板
6. 按照模板格式填写数据
7. 选择填写好的Excel文件
8. 点击"确定"开始导入
9. 系统处理导入结果并显示反馈

## 注意事项

### 后端开发要求
1. 需要实现 `/user/sysUser/batchImportLoanStaff` 接口处理批量导入
2. 需要实现 `/user/sysUser/loanStaffImportTemplate` 接口提供模板下载
3. 导入接口需要处理人员类型和岗位参数
4. 需要返回标准的响应格式

### 前端注意点
1. 文件格式限制为 .xlsx 和 .xls
2. 表单验证确保必填项不为空
3. 上传过程中显示加载状态
4. 错误处理和用户提示完善

### 权限控制
- 使用 `v-hasPermi="['sys:base:user:add']"` 控制按钮显示
- 确保用户有相应的操作权限

## 功能特点

1. **用户体验友好** - 界面简洁，操作流程清晰
2. **数据验证完善** - 前端表单验证 + 后端数据校验
3. **错误处理机制** - 完善的错误提示和处理
4. **样式统一** - 与现有系统界面风格保持一致
5. **功能完整** - 包含模板下载、文件上传、数据导入全流程

## 扩展性

该实现具有良好的扩展性，可以根据需要：
- 添加更多必填项
- 修改验证规则
- 扩展文件格式支持
- 增加导入进度显示
- 添加导入结果详情展示

## 总结

本次实现完全满足了需求：
- ✅ 借鉴了账户充值页面的批量导入功能
- ✅ 成功移植到借调人员页面
- ✅ 修改必填项为人员类型（选择框）和岗位（输入框）
- ✅ 保持了良好的用户体验和代码质量
- ✅ 提供了完整的功能实现和文档说明
